package orderbookindexer

import (
	"context"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/ProjectsTask/EasySwapBase/chain/chainclient"
	"github.com/ProjectsTask/EasySwapBase/chain/types"
	"github.com/ProjectsTask/EasySwapBase/logger/xzap"
	"github.com/ProjectsTask/EasySwapBase/ordermanager"
	"github.com/ProjectsTask/EasySwapBase/stores/gdb"
	"github.com/ProjectsTask/EasySwapBase/stores/gdb/orderbookmodel/base"
	"github.com/ProjectsTask/EasySwapBase/stores/gdb/orderbookmodel/multi"
	"github.com/ProjectsTask/EasySwapBase/stores/xkv"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	ethereumTypes "github.com/ethereum/go-ethereum/core/types"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/zeromicro/go-zero/core/threading"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/ProjectsTask/EasySwapSync/service/comm"
	"github.com/ProjectsTask/EasySwapSync/service/config"
)

const (
	EventIndexType  = 6  // 数据库中订单簿索引状态表的索引类型
	SleepInterval   = 10 // 同步循环的等待间隔
	SyncBlockPeriod = 10 // 每次同步的区块数量
	LogMakeTopic    = "0xfc37f2ff950f95913eb7182357ba3c14df60ef354bc7d6ab1ba2815f249fffe6"
	LogCancelTopic  = "0x0ac8bb53fac566d7afc05d8b4df11d7690a7b27bdc40b54e4060f9b21fb849bd"
	LogMatchTopic   = "0xf629aecab94607bc43ce4aebd564bf6e61c7327226a797b002de724b9944b20e"
	// go调用合约的abi实例
	contractAbi      = `[{"inputs":[],"name":"CannotFindNextEmptyKey","type":"error"},{"inputs":[],"name":"CannotFindPrevEmptyKey","type":"error"},{"inputs":[{"internalType":"OrderKey","name":"orderKey","type":"bytes32"}],"name":"CannotInsertDuplicateOrder","type":"error"},{"inputs":[],"name":"CannotInsertEmptyKey","type":"error"},{"inputs":[],"name":"CannotInsertExistingKey","type":"error"},{"inputs":[],"name":"CannotRemoveEmptyKey","type":"error"},{"inputs":[],"name":"CannotRemoveMissingKey","type":"error"},{"inputs":[],"name":"EnforcedPause","type":"error"},{"inputs":[],"name":"ExpectedPause","type":"error"},{"inputs":[],"name":"InvalidInitialization","type":"error"},{"inputs":[],"name":"NotInitializing","type":"error"},{"inputs":[{"internalType":"address","name":"owner","type":"address"}],"name":"OwnableInvalidOwner","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"OwnableUnauthorizedAccount","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"offset","type":"uint256"},{"indexed":false,"internalType":"bytes","name":"msg","type":"bytes"}],"name":"BatchMatchInnerError","type":"event"},{"anonymous":false,"inputs":[],"name":"EIP712DomainChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint64","name":"version","type":"uint64"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"OrderKey","name":"orderKey","type":"bytes32"},{"indexed":true,"internalType":"address","name":"maker","type":"address"}],"name":"LogCancel","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"OrderKey","name":"orderKey","type":"bytes32"},{"indexed":true,"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"indexed":true,"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"indexed":true,"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"indexed":false,"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"indexed":false,"internalType":"Price","name":"price","type":"uint128"},{"indexed":false,"internalType":"uint64","name":"expiry","type":"uint64"},{"indexed":false,"internalType":"uint64","name":"salt","type":"uint64"}],"name":"LogMake","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"OrderKey","name":"makeOrderKey","type":"bytes32"},{"indexed":true,"internalType":"OrderKey","name":"takeOrderKey","type":"bytes32"},{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"indexed":false,"internalType":"structLibOrder.Order","name":"makeOrder","type":"tuple"},{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"indexed":false,"internalType":"structLibOrder.Order","name":"takeOrder","type":"tuple"},{"indexed":false,"internalType":"uint128","name":"fillPrice","type":"uint128"}],"name":"LogMatch","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"OrderKey","name":"orderKey","type":"bytes32"},{"indexed":false,"internalType":"uint64","name":"salt","type":"uint64"}],"name":"LogSkipOrder","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"uint128","name":"newProtocolShare","type":"uint128"}],"name":"LogUpdatedProtocolShare","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"recipient","type":"address"},{"indexed":false,"internalType":"uint256","name":"amount","type":"uint256"}],"name":"LogWithdrawETH","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"previousOwner","type":"address"},{"indexed":true,"internalType":"address","name":"newOwner","type":"address"}],"name":"OwnershipTransferred","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Paused","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Unpaused","type":"event"},{"inputs":[{"internalType":"OrderKey[]","name":"orderKeys","type":"bytes32[]"}],"name":"cancelOrders","outputs":[{"internalType":"bool[]","name":"successes","type":"bool[]"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"components":[{"internalType":"OrderKey","name":"oldOrderKey","type":"bytes32"},{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"internalType":"structLibOrder.Order","name":"newOrder","type":"tuple"}],"internalType":"structLibOrder.EditDetail[]","name":"editDetails","type":"tuple[]"}],"name":"editOrders","outputs":[{"internalType":"OrderKey[]","name":"newOrderKeys","type":"bytes32[]"}],"stateMutability":"payable","type":"function"},{"inputs":[],"name":"eip712Domain","outputs":[{"internalType":"bytes1","name":"fields","type":"bytes1"},{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"version","type":"string"},{"internalType":"uint256","name":"chainId","type":"uint256"},{"internalType":"address","name":"verifyingContract","type":"address"},{"internalType":"bytes32","name":"salt","type":"bytes32"},{"internalType":"uint256[]","name":"extensions","type":"uint256[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"OrderKey","name":"","type":"bytes32"}],"name":"filledAmount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"}],"name":"getBestOrder","outputs":[{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"internalType":"structLibOrder.Order","name":"orderResult","type":"tuple"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"collection","type":"address"},{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"}],"name":"getBestPrice","outputs":[{"internalType":"Price","name":"price","type":"uint128"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"collection","type":"address"},{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"Price","name":"price","type":"uint128"}],"name":"getNextBestPrice","outputs":[{"internalType":"Price","name":"nextBestPrice","type":"uint128"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"uint256","name":"count","type":"uint256"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"OrderKey","name":"firstOrderKey","type":"bytes32"}],"name":"getOrders","outputs":[{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"internalType":"structLibOrder.Order[]","name":"resultOrders","type":"tuple[]"},{"internalType":"OrderKey","name":"nextOrderKey","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint128","name":"newProtocolShare","type":"uint128"},{"internalType":"address","name":"newVault","type":"address"},{"internalType":"string","name":"EIP712Name","type":"string"},{"internalType":"string","name":"EIP712Version","type":"string"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"internalType":"structLibOrder.Order[]","name":"newOrders","type":"tuple[]"}],"name":"makeOrders","outputs":[{"internalType":"OrderKey[]","name":"newOrderKeys","type":"bytes32[]"}],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"internalType":"structLibOrder.Order","name":"sellOrder","type":"tuple"},{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"internalType":"structLibOrder.Order","name":"buyOrder","type":"tuple"}],"name":"matchOrder","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"internalType":"structLibOrder.Order","name":"sellOrder","type":"tuple"},{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"internalType":"structLibOrder.Order","name":"buyOrder","type":"tuple"},{"internalType":"uint256","name":"msgValue","type":"uint256"}],"name":"matchOrderWithoutPayback","outputs":[{"internalType":"uint128","name":"costValue","type":"uint128"}],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"internalType":"structLibOrder.Order","name":"sellOrder","type":"tuple"},{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"internalType":"structLibOrder.Order","name":"buyOrder","type":"tuple"}],"internalType":"structLibOrder.MatchDetail[]","name":"matchDetails","type":"tuple[]"}],"name":"matchOrders","outputs":[{"internalType":"bool[]","name":"successes","type":"bool[]"}],"stateMutability":"payable","type":"function"},{"inputs":[{"internalType":"address","name":"","type":"address"},{"internalType":"enumLibOrder.Side","name":"","type":"uint8"},{"internalType":"Price","name":"","type":"uint128"}],"name":"orderQueues","outputs":[{"internalType":"OrderKey","name":"head","type":"bytes32"},{"internalType":"OrderKey","name":"tail","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"OrderKey","name":"","type":"bytes32"}],"name":"orders","outputs":[{"components":[{"internalType":"enumLibOrder.Side","name":"side","type":"uint8"},{"internalType":"enumLibOrder.SaleKind","name":"saleKind","type":"uint8"},{"internalType":"address","name":"maker","type":"address"},{"components":[{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"address","name":"collection","type":"address"},{"internalType":"uint96","name":"amount","type":"uint96"}],"internalType":"structLibOrder.Asset","name":"nft","type":"tuple"},{"internalType":"Price","name":"price","type":"uint128"},{"internalType":"uint64","name":"expiry","type":"uint64"},{"internalType":"uint64","name":"salt","type":"uint64"}],"internalType":"structLibOrder.Order","name":"order","type":"tuple"},{"internalType":"OrderKey","name":"next","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"owner","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"paused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"","type":"address"},{"internalType":"enumLibOrder.Side","name":"","type":"uint8"}],"name":"priceTrees","outputs":[{"internalType":"Price","name":"root","type":"uint128"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"protocolShare","outputs":[{"internalType":"uint128","name":"","type":"uint128"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"renounceOwnership","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint128","name":"newProtocolShare","type":"uint128"}],"name":"setProtocolShare","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newVault","type":"address"}],"name":"setVault","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newOwner","type":"address"}],"name":"transferOwnership","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"unpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"recipient","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"withdrawETH","outputs":[],"stateMutability":"nonpayable","type":"function"},{"stateMutability":"payable","type":"receive"}]`
	FixForCollection = 0 // 针对集合的订单
	FixForItem       = 1 // 针对具体的NFT的订单
	List             = 0
	Bid              = 1

	HexPrefix   = "0x"
	ZeroAddress = "******************************************"
)

type Order struct {
	Side     uint8 //随机数
	SaleKind uint8
	Maker    common.Address
	Nft      struct {
		TokenId        *big.Int       //NFT标识
		CollectionAddr common.Address // NFT合约地址
		Amount         *big.Int       // NFT价格
	}
	Price  *big.Int // 价格
	Expiry uint64
	Salt   uint64
}

type Service struct {
	ctx          context.Context            // 上下文
	cfg          *config.Config             // 配置
	db           *gorm.DB                   // 数据库
	kv           *xkv.Store                 // kv存储 不限于redis 这里用于缓存
	orderManager *ordermanager.OrderManager // 订单管理
	chainClient  chainclient.ChainClient    // 链客户端
	chainId      int64                      // 链ID
	chain        string                     // 链名称
	parsedAbi    abi.ABI                    // 解析abi
}

var MultiChainMaxBlockDifference = map[string]uint64{
	"eth":        1,
	"optimism":   2,
	"starknet":   1,
	"arbitrum":   2,
	"base":       2,
	"zksync-era": 2,
}

/*
子集合，主集合下面的子集合，可以直接通过service访问
集合了:
链基础方法(chainClient)、
订单管理相关方法(orderManager)、
abi解析合约方法(parsedAbi)、
数据库(db)、
kv存储(kv)、
链ID(chainId)、
链名称(chain)
*/
func New(ctx context.Context, cfg *config.Config, db *gorm.DB, xkv *xkv.Store, chainClient chainclient.ChainClient, chainId int64, chain string, orderManager *ordermanager.OrderManager) *Service {
	parsedAbi, _ := abi.JSON(strings.NewReader(contractAbi)) // 通过ABI实例化合约
	return &Service{
		ctx:          ctx,
		cfg:          cfg,
		db:           db,
		kv:           xkv,
		chainClient:  chainClient,
		orderManager: orderManager,
		chain:        chain,
		chainId:      chainId,
		parsedAbi:    parsedAbi,
	}
}

// 启动 订单簿同步服务
func (s *Service) Start() {
	// 启动订单簿同步事件循环  开启协程 后台运行
	// GoSafe跟groutine区别是：GoSafe会捕获panic，并打印错误日日志 不影响程序运行，而groutine会程序奔溃
	threading.GoSafe(s.SyncOrderBookEventLoop)

	// 启动集合地板价更新事件循环  开启协程 后台运行
	threading.GoSafe(s.UpKeepingCollectionFloorChangeLoop)
}

/*
同步订单簿事件循环 创建订单 取消订单 撮合订单
*/
func (s *Service) SyncOrderBookEventLoop() {
	// 定义一个base.IndexedStatus 类型变量，存储数据库中查询出来的索引状态信息
	var indexedStatus base.IndexedStatus

	// 目的：根据链ID和事件类型，从数据库中查询出最后同步的区块高度，为了避免重复同步
	// 使用gorma查询数据库 IndexedStatusTableName表
	// 查询条件为链ID chain_id == s.chainId,事件类型为 EventIndexType的数据
	// First获取第一条匹配的记录，并且填充到indexedStatus中，
	if err := s.db.WithContext(s.ctx).Table(base.IndexedStatusTableName()).
		Where("chain_id = ? and index_type = ?", s.chainId, EventIndexType).
		First(&indexedStatus).Error;
	// 如果查询错误，使用zapr日志库记录错误信息
	err != nil {
		xzap.WithContext(s.ctx).Error("failed on get listing index status", zap.Error(err))
		return
	}

	// 因为查询是第一条记录也是最新的一条记录  记录最后一个区块高度
	// 所以lastSyncBlock的值为indexedStatus.LastIndexedBlock(最后同步的区块高度)
	lastSyncBlock := uint64(indexedStatus.LastIndexedBlock)

	// 使用for循环，轮询获取当前区块高度
	for {
		select {
		case <-s.ctx.Done(): // 假设存在一个取消信号
			xzap.WithContext(s.ctx).Info("SyncOrderBookEventLoop stopped due to context cancellation")
			return
		default:
		}

		//直接调用子集合中chainClient 的GetBlockNumber方法获取当前链的区块高度(链基础方法)
		currentBlockNum, err := s.chainClient.BlockNumber() // 以轮询的方式获取当前区块高度
		if err != nil {
			xzap.WithContext(s.ctx).Error("failed on get current block number", zap.Error(err))
			time.Sleep(SleepInterval * time.Second) // 如果获取当前区块高度失败，等待10秒时间后再次轮询
			continue                                // 继续下一次循环
		}

		// 如果上次同步的区块高度大于当前区块高度，等待一段时间后再次轮询
		if lastSyncBlock > currentBlockNum-MultiChainMaxBlockDifference[s.chain] { // 如果上次同步的区块高度大于当前区块高度，等待一段时间后再次轮询
			time.Sleep(SleepInterval * time.Second)
			continue
		}

		// 如果结束区块高度大于当前区块高度，将结束区块高度设置为当前区块高度
		startBlock := lastSyncBlock
		endBlock := startBlock + SyncBlockPeriod
		if endBlock > currentBlockNum-MultiChainMaxBlockDifference[s.chain] {
			endBlock = currentBlockNum - MultiChainMaxBlockDifference[s.chain]
		}

		// 定义区块查询条件
		query := types.FilterQuery{
			FromBlock: new(big.Int).SetUint64(startBlock),
			ToBlock:   new(big.Int).SetUint64(endBlock),
			Addresses: []string{s.cfg.ContractCfg.DexAddress},
		}
		//同时获取多个（SyncBlockPeriod）区块的日志
		// 查询第x到y区块中，EasySwap合约产生的所有的事件
		logs, err := s.chainClient.FilterLogs(s.ctx, query)
		if err != nil {
			xzap.WithContext(s.ctx).Error("failed on get log", zap.Error(err))
			time.Sleep(SleepInterval * time.Second)
			continue // 获取日志失败，等待并重试
		}

		for _, log := range logs { // 遍历日志，根据不同的topic处理不同的事件
			// 类型断言 将通用的log转成以太坊特定的log类型
			ethLog := log.(ethereumTypes.Log)
			switch ethLog.Topics[0].String() { //Topics[0]是事件的哈希值，识别事件类型
			case LogMakeTopic: //用户创建订单类型
				s.handleMakeEvent(ethLog)
			case LogCancelTopic: // 用户取消订单类型
				s.handleCancelEvent(ethLog)
			case LogMatchTopic: // 用户撮合成功交易类型
				s.handleMatchEvent(ethLog)
			default:
			}
		}

		lastSyncBlock = endBlock + 1 // 更新最后同步的区块高度 下一次循环从下一块开始
		// 更新数据库中最后同步的区块高度
		if err := s.db.WithContext(s.ctx).Table(base.IndexedStatusTableName()).
			Where("chain_id = ? and index_type = ?", s.chainId, EventIndexType).
			Update("last_indexed_block", lastSyncBlock).Error; err != nil {
			xzap.WithContext(s.ctx).Error("failed on update orderbook event sync block number",
				zap.Error(err))
			return
		}
		// 记录日志
		xzap.WithContext(s.ctx).Info("sync orderbook event ...",
			zap.Uint64("start_block", startBlock),
			zap.Uint64("end_block", endBlock))
	}
}

/*
处理挂单事件  用户前端创建订单 卖单/买单
1.接收区块链日志数据，解析订单数据
2.将订单数据保存的订单表中(加入订单簿)
 3. 将创建订单的数据保存到历史记录表中(一般不修改)
 4. 将订单信息存到订单管理队列中，用于后续的订单状态同步和过期检查90天 为什么要有这个队列？
    4.1 这个对列是一个redis队列，用于存储订单信息，用于后续的订单状态同步和过期检查
    4.2 会开启一个协程，不断的去检查新插入进来的订单和待处理的订单，如果订单状态发生变化，则更新订单状态到订单表中
    4.3 减轻订单表读写压力，性能提升
*/
func (s *Service) handleMakeEvent(log ethereumTypes.Log) {
	// 挂单事件的数据结构
	var event struct {
		OrderKey [32]byte // 订单key
		Nft      struct { // NFT数据结构
			TokenId        *big.Int
			CollectionAddr common.Address // NFT合约地址
			Amount         *big.Int       // NFT数量
		}
		Price  *big.Int // 价格
		Expiry uint64   // 到期时间
		Salt   uint64   // 随机数
	}

	// 通过ABI解析日志数据
	err := s.parsedAbi.UnpackIntoInterface(&event, "LogMake", log.Data)
	if err != nil {
		xzap.WithContext(s.ctx).Error("Error unpacking LogMake event:", zap.Error(err))
		return
	}
	// 场景: 解析事件的基本信息: 订单方向、类型、下单人
	// 把区块链的muit 转换成uint8 转换成可识别的业务信息
	// 买单/卖单
	side := uint8(new(big.Int).SetBytes(log.Topics[1].Bytes()).Uint64())
	// 销售类型 (0: 针对具体的NFT, 1: 针对集合)
	saleKind := uint8(new(big.Int).SetBytes(log.Topics[2].Bytes()).Uint64())
	// 卖家用户地址
	maker := common.BytesToAddress(log.Topics[3].Bytes())

	var orderType int64
	if side == Bid { // 买单
		if saleKind == FixForCollection { // 针对集合的买单
			orderType = multi.CollectionBidOrder
		} else { // 针对某个具体NFT的买单
			orderType = multi.ItemBidOrder
		}
	} else { // 卖单
		orderType = multi.ListingOrder
	}
	newOrder := multi.Order{
		CollectionAddress: event.Nft.CollectionAddr.String(),                 // NFT集合的智能合约地址
		MarketplaceId:     multi.MarketOrderBook,                             // 交易市场标识符
		TokenId:           event.Nft.TokenId.String(),                        // NFT代币的唯一标识ID
		OrderID:           HexPrefix + hex.EncodeToString(event.OrderKey[:]), // 订单的唯一标识符
		OrderStatus:       multi.OrderStatusActive,                           // 订单状态（当前为活跃状态）
		EventTime:         time.Now().Unix(),                                 // 订单创建时间戳
		ExpireTime:        int64(event.Expiry),                               // 订单过期时间戳
		CurrencyAddress:   s.cfg.ContractCfg.EthAddress,                      // 支付货币的合约地址（ETH）
		Price:             decimal.NewFromBigInt(event.Price, 0),             // 订单价格
		Maker:             maker.String(),                                    // 订单发起方地址
		Taker:             ZeroAddress,                                       // 订单接受方地址（初始为空）
		QuantityRemaining: event.Nft.Amount.Int64(),                          // 订单剩余可交易数量
		Size:              event.Nft.Amount.Int64(),                          // 订单总数量
		OrderType:         orderType,                                         // 订单类型（买单/卖单）
		Salt:              int64(event.Salt),                                 // 订单随机数（防重放攻击）
	}

	/*	系统创建一个买单/买单，记录到订单表 存储订单的状态
		    解决目的: 区块链事件中，可能会重复接收到相同的事件，导致数据库数据重复
			1. Clauses  GORM中用来添加SQL子句的方法
			2. clause.OnConflict  处理数据库冲突的子句
			3. DoNothing: true  当发生冲突时，什么都不做（忽略这次插入）
	*/
	if err := s.db.WithContext(s.ctx).Table(multi.OrderTableName(s.chain)).Clauses(clause.OnConflict{
		DoNothing: true,
	}).Create(&newOrder).Error; err != nil { // 将订单信息存入数据库
		xzap.WithContext(s.ctx).Error("failed on create order",
			zap.Error(err))
	}
	// 获取区块时间
	blockTime, err := s.chainClient.BlockTimeByNumber(s.ctx, big.NewInt(int64(log.BlockNumber)))
	if err != nil {
		xzap.WithContext(s.ctx).Error("failed to get block time", zap.Error(err))
		return
	}
	var activityType int
	if side == Bid { // 买单
		if saleKind == FixForCollection {
			activityType = multi.CollectionBid
		} else {
			activityType = multi.ItemBid
		}
	} else {
		activityType = multi.Listing
	}
	newActivity := multi.Activity{ // 将订单信息存入活动表
		ActivityType:      activityType,                          // 活动类型
		Maker:             maker.String(),                        // 订单创建者
		Taker:             ZeroAddress,                           // 订单接收者
		MarketplaceID:     multi.MarketOrderBook,                 // 市场ID
		CollectionAddress: event.Nft.CollectionAddr.String(),     // NFT集合的智能合约地址
		TokenId:           event.Nft.TokenId.String(),            // NFT代币的唯一标识ID
		CurrencyAddress:   s.cfg.ContractCfg.EthAddress,          // 订单的支付货币地址
		Price:             decimal.NewFromBigInt(event.Price, 0), // 订单价格
		BlockNumber:       int64(log.BlockNumber),                // 订单的块号
		TxHash:            log.TxHash.String(),                   // 交易哈希
		EventTime:         int64(blockTime),                      // 订单事件发生时间
	}

	/*
	  那订单表存储了，是会修改的，所以这里要分一个交易活动历史记录表再存储一下
	  活动表 也可以叫做交易活动历史表，存储了历史记录，一般不会修改。
	*/
	if err := s.db.WithContext(s.ctx).Table(multi.ActivityTableName(s.chain)).Clauses(clause.OnConflict{
		DoNothing: true,
	}).Create(&newActivity).Error; err != nil {
		xzap.WithContext(s.ctx).Warn("failed on create activity",
			zap.Error(err))
	}

	/*
	 加入订单管理队列
	 1.订单过期检查
	 2. 订单状态同步
	*/
	if err := s.orderManager.AddToOrderManagerQueue(&multi.Order{ // 将订单信息存入订单管理队列
		ExpireTime:        newOrder.ExpireTime,
		OrderID:           newOrder.OrderID,
		CollectionAddress: newOrder.CollectionAddress,
		TokenId:           newOrder.TokenId,
		Price:             newOrder.Price,
		Maker:             newOrder.Maker,
	}); err != nil {
		xzap.WithContext(s.ctx).Error("failed on add order to manager queue",
			zap.Error(err),
			zap.String("order_id", newOrder.OrderID))
	}
}

func (s *Service) handleMatchEvent(log ethereumTypes.Log) {
	// 第1步：解析区块链事件数据
	// 定义一个结构体来存储撮合事件的数据（包括买单、卖单和成交价格）
	var event struct {
		MakeOrder Order    // 第一个订单（可能是买单或卖单）
		TakeOrder Order    // 第二个订单（可能是买单或卖单）
		FillPrice *big.Int // 实际成交价格
	}
	// 使用ABI（应用程序二进制接口）解析区块链原始日志数据，转换成我们能理解的格式
	err := s.parsedAbi.UnpackIntoInterface(&event, "LogMatch", log.Data)
	if err != nil {
		// 如果解析失败，记录错误并退出函数
		xzap.WithContext(s.ctx).Error("Error unpacking LogMatch event:", zap.Error(err))
		return
	}
	// 从区块链事件的Topics中获取订单ID
	// Topics[1]存储第一个订单ID（makeOrder）
	makeOrderId := HexPrefix + hex.EncodeToString(log.Topics[1].Bytes())
	// Topics[2]存储第二个订单ID（takeOrder）
	takeOrderId := HexPrefix + hex.EncodeToString(log.Topics[2].Bytes())

	// 第2步：准备处理订单信息
	// 声明变量用于存储交易相关信息
	var owner string         // NFT的新所有者
	var collection string    // NFT所属的集合地址
	var tokenId string       // NFT的唯一标识符
	var from string          // 交易发送方（卖家）
	var to string            // 交易接收方（买家）
	var sellOrderId string   // 卖单ID
	var buyOrder multi.Order // 买单信息

	// 第3步：根据订单类型处理不同的撮合情况
	// 判断第一个订单(MakeOrder)是买单还是卖单
	if event.MakeOrder.Side == Bid { // 如果MakeOrder是买单（由卖方发起交易撮合）
		owner = strings.ToLower(event.MakeOrder.Maker.String())
		collection = event.TakeOrder.Nft.CollectionAddr.String()
		tokenId = event.TakeOrder.Nft.TokenId.String()
		from = event.TakeOrder.Maker.String()
		to = event.MakeOrder.Maker.String()
		sellOrderId = takeOrderId

		// 更新卖方订单状态
		if err := s.db.WithContext(s.ctx).Table(multi.OrderTableName(s.chain)).
			Where("order_id = ?", takeOrderId).
			Updates(map[string]interface{}{
				"order_status":       multi.OrderStatusFilled,
				"quantity_remaining": 0,
				"taker":              to,
			}).Error; err != nil {
			xzap.WithContext(s.ctx).Error("failed on update order status",
				zap.String("order_id", takeOrderId))
			return
		}

		// 查询买方订单信息，不存在则无需更新，说明不是从平台前端发起的交易
		if err := s.db.WithContext(s.ctx).Table(multi.OrderTableName(s.chain)).
			Where("order_id = ?", makeOrderId).
			First(&buyOrder).Error; err != nil {
			xzap.WithContext(s.ctx).Error("failed on get buy order",
				zap.Error(err))
			return
		}
		// 更新买方订单的剩余数量
		if buyOrder.QuantityRemaining > 1 {
			if err := s.db.WithContext(s.ctx).Table(multi.OrderTableName(s.chain)).
				Where("order_id = ?", makeOrderId).
				Update("quantity_remaining", buyOrder.QuantityRemaining-1).Error; err != nil {
				xzap.WithContext(s.ctx).Error("failed on update order quantity_remaining",
					zap.String("order_id", makeOrderId))
				return
			}
		} else {
			if err := s.db.WithContext(s.ctx).Table(multi.OrderTableName(s.chain)).
				Where("order_id = ?", makeOrderId).
				Updates(map[string]interface{}{
					"order_status":       multi.OrderStatusFilled,
					"quantity_remaining": 0,
				}).Error; err != nil {
				xzap.WithContext(s.ctx).Error("failed on update order status",
					zap.String("order_id", makeOrderId))
				return
			}
		}
	} else { // 卖单， 由买方发起交易撮合， 同理
		owner = strings.ToLower(event.TakeOrder.Maker.String())
		collection = event.MakeOrder.Nft.CollectionAddr.String()
		tokenId = event.MakeOrder.Nft.TokenId.String()
		from = event.MakeOrder.Maker.String()
		to = event.TakeOrder.Maker.String()
		sellOrderId = makeOrderId

		if err := s.db.WithContext(s.ctx).Table(multi.OrderTableName(s.chain)).
			Where("order_id = ?", makeOrderId).
			Updates(map[string]interface{}{
				"order_status":       multi.OrderStatusFilled,
				"quantity_remaining": 0,
				"taker":              to,
			}).Error; err != nil {
			xzap.WithContext(s.ctx).Error("failed on update order status",
				zap.String("order_id", makeOrderId))
			return
		}

		if err := s.db.WithContext(s.ctx).Table(multi.OrderTableName(s.chain)).
			Where("order_id = ?", takeOrderId).
			First(&buyOrder).Error; err != nil {
			xzap.WithContext(s.ctx).Error("failed on get buy order",
				zap.Error(err))
			return
		}
		if buyOrder.QuantityRemaining > 1 {
			if err := s.db.WithContext(s.ctx).Table(multi.OrderTableName(s.chain)).
				Where("order_id = ?", takeOrderId).
				Update("quantity_remaining", buyOrder.QuantityRemaining-1).Error; err != nil {
				xzap.WithContext(s.ctx).Error("failed on update order quantity_remaining",
					zap.String("order_id", takeOrderId))
				return
			}
		} else {
			if err := s.db.WithContext(s.ctx).Table(multi.OrderTableName(s.chain)).
				Where("order_id = ?", takeOrderId).
				Updates(map[string]interface{}{
					"order_status":       multi.OrderStatusFilled,
					"quantity_remaining": 0,
				}).Error; err != nil {
				xzap.WithContext(s.ctx).Error("failed on update order status",
					zap.String("order_id", takeOrderId))
				return
			}
		}
	}

	// 第4步：记录交易活动并更新相关数据
	// 获取区块的时间戳
	blockTime, err := s.chainClient.BlockTimeByNumber(s.ctx, big.NewInt(int64(log.BlockNumber)))
	if err != nil {
		xzap.WithContext(s.ctx).Error("failed to get block time", zap.Error(err))
		return
	}

	// 创建一个新的交易活动记录
	newActivity := multi.Activity{
		ActivityType:      multi.Sale,                                // 活动类型：销售
		Maker:             event.MakeOrder.Maker.String(),            // 第一个订单的创建者
		Taker:             event.TakeOrder.Maker.String(),            // 第二个订单的创建者
		MarketplaceID:     multi.MarketOrderBook,                     // 市场ID
		CollectionAddress: collection,                                // NFT集合地址
		TokenId:           tokenId,                                   // NFT ID
		CurrencyAddress:   s.cfg.ContractCfg.EthAddress,              // 支付货币地址
		Price:             decimal.NewFromBigInt(event.FillPrice, 0), // 成交价格
		BlockNumber:       int64(log.BlockNumber),                    // 区块号
		TxHash:            log.TxHash.String(),                       // 交易哈希
		EventTime:         int64(blockTime),                          // 事件时间
	}

	// 将交易活动保存到数据库中（如果已存在相同记录则不做任何操作）
	if err := s.db.WithContext(s.ctx).Table(multi.ActivityTableName(s.chain)).Clauses(clause.OnConflict{
		DoNothing: true,
	}).Create(&newActivity).Error; err != nil {
		xzap.WithContext(s.ctx).Warn("failed on create activity",
			zap.Error(err))
	}

	// 更新NFT的所有者信息
	if err := s.db.WithContext(s.ctx).Table(multi.ItemTableName(s.chain)).
		Where("collection_address = ? and token_id = ?", strings.ToLower(collection), tokenId).
		Update("owner", owner).Error; err != nil {
		xzap.WithContext(s.ctx).Error("failed to update item owner",
			zap.Error(err))
		return
	}

	// 将交易信息添加到价格更新队列，用于后续更新集合地板价
	if err := ordermanager.AddUpdatePriceEvent(s.kv, &ordermanager.TradeEvent{
		OrderId:        sellOrderId,      // 卖单ID
		CollectionAddr: collection,       // NFT集合地址
		EventType:      ordermanager.Buy, // 事件类型：购买
		TokenID:        tokenId,          // NFT ID
		From:           from,             // 卖家地址
		To:             to,               // 买家地址
	}, s.chain); err != nil {
		xzap.WithContext(s.ctx).Error("failed on add update price event",
			zap.Error(err),
			zap.String("type", "sale"),
			zap.String("order_id", sellOrderId))
	}
}

func (s *Service) handleCancelEvent(log ethereumTypes.Log) {
	orderId := HexPrefix + hex.EncodeToString(log.Topics[1].Bytes()) // 通过topic获取订单ID
	//maker := common.BytesToAddress(log.Topics[2].Bytes())
	// 查询订单表，更新订单状态为已取消
	if err := s.db.WithContext(s.ctx).Table(multi.OrderTableName(s.chain)).
		Where("order_id = ?", orderId).
		Update("order_status", multi.OrderStatusCancelled).Error; err != nil {
		xzap.WithContext(s.ctx).Error("failed on update order status",
			zap.String("order_id", orderId))
		return
	}

	var cancelOrder multi.Order
	// 查询订单表，获取订单信息
	if err := s.db.WithContext(s.ctx).Table(multi.OrderTableName(s.chain)).
		Where("order_id = ?", orderId).
		First(&cancelOrder).Error; err != nil {
		xzap.WithContext(s.ctx).Error("failed on get cancel order",
			zap.Error(err))
		return
	}

	// 获取区块时间
	blockTime, err := s.chainClient.BlockTimeByNumber(s.ctx, big.NewInt(int64(log.BlockNumber)))
	if err != nil {
		xzap.WithContext(s.ctx).Error("failed to get block time", zap.Error(err))
		return
	}
	var activityType int
	// 根据取消订单的类型，设置活动类型
	if cancelOrder.OrderType == multi.ListingOrder { // 卖单
		activityType = multi.CancelListing
	} else if cancelOrder.OrderType == multi.CollectionBidOrder { // 针对集合的买单
		activityType = multi.CancelCollectionBid
	} else { // 针对某个具体NFT的买单
		activityType = multi.CancelItemBid
	}
	newActivity := multi.Activity{ // 将取消订单信息存入活动表 也就是交易活动历史表
		ActivityType:      activityType,
		Maker:             cancelOrder.Maker,
		Taker:             ZeroAddress,
		MarketplaceID:     multi.MarketOrderBook,
		CollectionAddress: cancelOrder.CollectionAddress,
		TokenId:           cancelOrder.TokenId,
		CurrencyAddress:   s.cfg.ContractCfg.EthAddress,
		Price:             cancelOrder.Price,
		BlockNumber:       int64(log.BlockNumber),
		TxHash:            log.TxHash.String(),
		EventTime:         int64(blockTime),
	}
	// 将取消订单信息存入活动表 也就是交易活动历史表
	if err := s.db.WithContext(s.ctx).Table(multi.ActivityTableName(s.chain)).Clauses(clause.OnConflict{
		DoNothing: true,
	}).Create(&newActivity).Error; err != nil {
		xzap.WithContext(s.ctx).Warn("failed on create activity",
			zap.Error(err))
	}
	// 将取消订单信息存入价格更新队列 目的：更新地板价 因为取消订单后，地板价可能发生变化
	if err := ordermanager.AddUpdatePriceEvent(s.kv, &ordermanager.TradeEvent{
		OrderId:        cancelOrder.OrderID,
		CollectionAddr: cancelOrder.CollectionAddress,
		TokenID:        cancelOrder.TokenId,
		EventType:      ordermanager.Cancel,
	}, s.chain); err != nil {
		xzap.WithContext(s.ctx).Error("failed on add update price event",
			zap.Error(err),
			zap.String("type", "cancel"),
			zap.String("order_id", cancelOrder.OrderID))
	}
}

// UpKeepingCollectionFloorChangeLoop 是一个持续运行的循环，负责维护NFT集合的地板价数据
// 主要功能：
// 1. 定期(每10秒)查询并更新所有NFT集合的当前地板价
// 2. 定期(每天)清理过期的地板价历史数据
// 3. 确保用户能够看到准确、及时的集合地板价信息
//
// 地板价(Floor Price)是NFT市场的重要指标，指的是某个NFT集合中最便宜的NFT的价格
// 这个价格对交易者做决策非常重要，所以需要频繁更新以保持数据的实时性
func (s *Service) UpKeepingCollectionFloorChangeLoop() {
	// 创建一个每天触发一次的定时器，用于清理过期的地板价数据记录
	// 这有助于维护数据库性能，防止存储过多历史数据
	timer := time.NewTicker(comm.DaySeconds * time.Second)
	defer timer.Stop() // 停止定时器
	// 创建一个每10分钟触发一次的定时器，用于更新当前集合的地板价
	// 频繁更新地板价可以确保用户看到相对实时的市场数据，提升用户体验
	updateFloorPriceTimer := time.NewTicker(comm.MaxCollectionFloorTimeDifference * time.Second)
	defer updateFloorPriceTimer.Stop() // 停止定时器

	// indexedStatus用于跟踪上次更新地板价的时间，帮助系统在重启后恢复状态
	var indexedStatus base.IndexedStatus
	// 从数据库中查询当前的索引状态(上次更新时间)
	if err := s.db.WithContext(s.ctx).Table(base.IndexedStatusTableName()).
		Select("last_indexed_time").
		Where("chain_id = ? and index_type = ?", s.chainId, comm.CollectionFloorChangeIndexType).
		First(&indexedStatus).Error; err != nil {
		xzap.WithContext(s.ctx).Error("failed on get collection floor change index status",
			zap.Error(err))
		return
	}

	// 进入无限循环，持续监听定时器事件
	for {
		select {
		// 监听上下文取消信号，如果收到则退出循环
		case <-s.ctx.Done():
			xzap.WithContext(s.ctx).Info("UpKeepingCollectionFloorChangeLoop stopped due to context cancellation")
			return
		// 每天执行一次，清理过期的地板价数据
		case <-timer.C:
			if err := s.deleteExpireCollectionFloorChangeFromDatabase(); err != nil {
				xzap.WithContext(s.ctx).Error("failed on delete expire collection floor change",
					zap.Error(err))
			}
		// 每10秒执行一次，查询并更新所有集合的最新地板价
		case <-updateFloorPriceTimer.C:
			// 检查当前项目名称是否为"OrderBookDexProject"(订单簿项目)
			// 系统可能运行多种项目，但只有订单簿项目需要更新地板价
			// 示例：如果ProjectCfg.Name="OrderBookDexProject"，则执行更新；其他项目名称则跳过
			if s.cfg.ProjectCfg.Name == gdb.OrderBookDexProject {
				// 查询所有NFT集合的当前地板价(每个集合中最低价格的NFT)
				// 这里会返回一个数组，包含多个集合的地板价信息，例如：
				// [
				//   {CollectionAddress: "0x123...", Price: 0.5 ETH},
				//   {CollectionAddress: "0x456...", Price: 1.2 ETH},
				//   {CollectionAddress: "0x789...", Price: 0.05 ETH}
				// ]
				floorPrices, err := s.QueryCollectionsFloorPrice()
				if err != nil {
					xzap.WithContext(s.ctx).Error("failed on query collections floor change",
						zap.Error(err))
					continue
				}

				// 将查询到的所有集合地板价数据批量保存到数据库中
				// 这里会将多个集合的地板价一次性保存，每批最多保存200条记录
				// 例如：将上面查询到的所有集合地板价[{0x123, 0.5}, {0x456, 1.2}, {0x789, 0.05}]保存到数据库
				if err := s.persistCollectionsFloorChange(floorPrices); err != nil {
					xzap.WithContext(s.ctx).Error("failed on persist collections floor price",
						zap.Error(err))
					continue
				}
			}
			// 注意：这里不需要default分支，因为我们希望在没有定时器事件时阻塞，而不是继续循环
		}
	}
}

// deleteExpireCollectionFloorChangeFromDatabase 从数据库中删除过期的集合地板价数据
//
// 目的：清理旧的地板价数据，避免数据库中积累过多历史数据，影响查询性能
//
// 工作流程：
// 1. 构建SQL删除语句，删除event_time早于当前时间减去CollectionFloorTimeRange的记录
// 2. 执行SQL语句删除过期数据
// 3. 如果删除操作失败，返回包装后的错误信息
//
// 返回值：
// - 如果删除成功，返回nil
// - 如果删除失败，返回错误信息
func (s *Service) deleteExpireCollectionFloorChangeFromDatabase() error {
	// 构建SQL删除语句：删除event_time早于当前时间减去CollectionFloorTimeRange(通常为7天)的记录
	stmt := fmt.Sprintf(`DELETE FROM %s where event_time < UNIX_TIMESTAMP() - %d`, gdb.GetMultiProjectCollectionFloorPriceTableName(s.cfg.ProjectCfg.Name, s.chain), comm.CollectionFloorTimeRange)

	// 执行SQL删除语句
	if err := s.db.Exec(stmt).Error; err != nil {
		// 如果删除操作失败，返回包装后的错误信息
		return errors.Wrap(err, "failed on delete expire collection floor price")
	}

	// 删除成功，返回nil
	return nil
}

// QueryCollectionsFloorPrice 查询所有NFT集合的当前地板价
//
// 目的：获取平台上所有NFT集合的最低售价信息，用于市场分析和前端展示
//
// 工作流程：
// 1. 获取当前时间戳，用于记录数据的事件时间和创建/更新时间
// 2. 构建SQL查询语句，从订单表和物品表中联合查询各集合的最低价格
// 3. 执行SQL查询，获取所有集合的地板价信息
// 4. 为每个集合的地板价记录添加时间戳信息
// 5. 返回包含所有集合地板价的数组
//
// 查询条件：
// - 只查询卖单类型(ListingType)的订单
// - 只查询状态为活跃(OrderStatusActive)的订单
// - 只查询未过期的订单(expire_time > 当前时间)
// - 只查询卖家是NFT所有者的订单(co.maker = ci.owner)
//
// 返回值：
// - 成功：返回包含所有集合地板价的数组，每个元素包含集合地址和最低价格
// - 失败：返回nil和错误信息
func (s *Service) QueryCollectionsFloorPrice() ([]multi.CollectionFloorPrice, error) {
	// 获取当前时间戳，用于记录数据的事件时间和创建/更新时间
	timestamp := time.Now().Unix()           // 秒级时间戳
	timestampMilli := time.Now().UnixMilli() // 毫秒级时间戳

	// 定义集合地板价数组，用于存储查询结果
	var collectionFloorPrice []multi.CollectionFloorPrice

	// 构建SQL查询语句：从订单表和物品表中联合查询各集合的最低价格
	// ci表示item表，co表示order表
	sql := fmt.Sprintf(`SELECT co.collection_address as collection_address,min(co.price) as price
FROM %s as ci
         left join %s co on co.collection_address = ci.collection_address and co.token_id = ci.token_id
WHERE (co.order_type = ? and
       co.order_status = ? and expire_time > ? and co.maker = ci.owner) group by co.collection_address`, gdb.GetMultiProjectItemTableName(s.cfg.ProjectCfg.Name, s.chain), gdb.GetMultiProjectOrderTableName(s.cfg.ProjectCfg.Name, s.chain))

	// 执行SQL查询，获取所有集合的地板价信息
	if err := s.db.WithContext(s.ctx).Raw(
		sql,
		multi.ListingType,       // 只查询卖单类型的订单
		multi.OrderStatusActive, // 只查询状态为活跃的订单
		time.Now().Unix(),       // 只查询未过期的订单
	).Scan(&collectionFloorPrice).Error; err != nil {
		// 查询失败，返回错误信息
		return nil, errors.Wrap(err, "failed on get collection floor price")
	}

	// 为每个集合的地板价记录添加时间戳信息
	for i := 0; i < len(collectionFloorPrice); i++ {
		collectionFloorPrice[i].EventTime = timestamp       // 事件时间
		collectionFloorPrice[i].CreateTime = timestampMilli // 创建时间
		collectionFloorPrice[i].UpdateTime = timestampMilli // 更新时间
	}

	// 返回包含所有集合地板价的数组
	return collectionFloorPrice, nil
}

// persistCollectionsFloorChange 将集合地板价数据保存到数据库
//
// 目的：批量保存或更新NFT集合的地板价数据，确保数据库中存储的是最新的地板价信息
//
// 参数：
// - FloorPrices: 包含多个NFT集合地板价信息的数组
//
// 工作流程：
// 1. 按批次处理数据，每批最多处理comm.DBBatchSizeLimit条记录(通常为200条)
// 2. 为每批数据构建批量插入的SQL语句和参数
// 3. 执行SQL语句，使用INSERT...ON DUPLICATE KEY UPDATE语法实现"存在则更新，不存在则插入"
// 4. 如果执行失败，返回错误信息
//
// 返回值：
// - 成功：返回nil
// - 失败：返回错误信息
func (s *Service) persistCollectionsFloorChange(FloorPrices []multi.CollectionFloorPrice) error {
	// 按批次处理数据，每批最多处理comm.DBBatchSizeLimit条记录(通常为200条)
	for i := 0; i < len(FloorPrices); i += comm.DBBatchSizeLimit {
		// 计算当前批次的结束索引
		end := i + comm.DBBatchSizeLimit
		// 确保结束索引不超过数组长度
		if i+comm.DBBatchSizeLimit >= len(FloorPrices) {
			end = len(FloorPrices)
		}

		// 准备SQL语句的值占位符和参数
		valueStrings := make([]string, 0)   // 存储SQL值占位符，如"(?,?,?,?,?)"
		valueArgs := make([]interface{}, 0) // 存储对应的参数值

		// 遍历当前批次的数据，构建SQL参数
		for _, t := range FloorPrices[i:end] {
			valueStrings = append(valueStrings, "(?,?,?,?,?)") // 每条记录5个字段
			valueArgs = append(valueArgs,
				t.CollectionAddress, // 集合地址
				t.Price,             // 地板价
				t.EventTime,         // 事件时间
				t.CreateTime,        // 创建时间
				t.UpdateTime)        // 更新时间
		}

		// 构建完整的SQL插入语句，使用ON DUPLICATE KEY UPDATE实现"存在则更新，不存在则插入"
		stmt := fmt.Sprintf(`INSERT INTO %s (collection_address,price,event_time,create_time,update_time)  VALUES %s
		ON DUPLICATE KEY UPDATE update_time=VALUES(update_time)`, gdb.GetMultiProjectCollectionFloorPriceTableName(s.cfg.ProjectCfg.Name, s.chain), strings.Join(valueStrings, ","))

		// 执行SQL语句
		if err := s.db.Exec(stmt, valueArgs...).Error; err != nil {
			// 执行失败，返回错误信息
			return errors.Wrap(err, "failed on persist collection floor price info")
		}
	}
	// 全部批次处理成功，返回nil
	return nil
}
