# EasySwapSync
EasySwapSync is a service that synchronizes EasySwap contract events from the blockchain to the database.

是一项将EasySwap合约事件从区块链同步到数据库的服务。

## Prerequisites
### Mysql & Redis
You should get your MYSQL & Redis running and create a database inside. MYSQL & Redis inside docker is recommended for local testing.
For example, if the machine is arm64 architecture, you can use the following docker-compose file to start mysql and redis.

你应该让你的MYSQL和Redis运行并在里面创建一个数据库。推荐使用 docker 中的 MYSQL 和 Redis 进行本地测试。
例如，如果机器是arm64架构，您可以使用以下docker-compose文件启动mysql和redis。

```shell
docker-compose -f docker-compose-arm64.yml up -d
```

For more information about the table creation statement, see the SQL file in the db/migrations directory.

有关表创建语句的更多信息，请参阅 db/migrations 目录中的 SQL 文件。

### Set Config file
Copy config/config.toml.example to config/config.toml. 
And modify the config file according to your environment, especially the mysql and redis connection information.
And set contract address in config file.

将 config/config.toml.example 复制到 config/config.toml。 （2）
并根据您的环境修改配置文件，特别是mysql和redis连接信息。
并在配置文件中设置合同地址。

## Run
Run command below
```shell
go run main.go daemon
```





## 1.项目流程

1. ```
   用户输入 make run 
   ```

2. ```
   Makefile 执行：
   
   # 构建程序
   go build main.go
   
   # 运行程序，注意这里有 daemon 参数
   ./build/sync_service.exe daemon -c ".\config\config_import.toml"
   ```

3. ```go
   程序启动流程：
   
   main.go 
     ↓ 调用
   cmd.Execute() (root.go)
     ↓ Cobra 解析命令行
   发现用户输入了 "daemon" 子命令--------------(跳转下个片段代码)
     ↓ Cobra 路由
   找到 DaemonCmd，执行其 Run 函数
     ↓ 执行
   runDaemon() 函数启动同步服务
   ```

   ```golang
   1. Execute() 解析参数
      ↓
   2. 发现有子命令 "daemon"
      ↓  
   3. 在 cmd 包里找到 daemon.go 文件
      ↓
   4. daemon.go 的 init() 自动执行
      ↓
   5. init() 中的 rootCmd.AddCommand(DaemonCmd) 将子命令注册
      ↓
   6. Cobra 找到 DaemonCmd，发现有 Run 函数
      ↓
   7. 执行 Run 函数中的业务逻辑 (runDaemon)
      ↓
   8. 创建所有服务的上下文，初始化日志模块、1.初始化服务、2.启动服务、3.优雅退出
      
   ```
   

#### 1.  初始化服务

- **初始化redis服务**，使用go-zero的kv-stoere的伪集群，将配置文件的redis节点遍历添加到kcConf切片中，创建一个应用层的切片。

- **优点:** 响应快 低延迟、不需要额外的运营成本。

- **缺点:**单点故障、没有自动故障转移

- **其他方案**：**redis原生集群**，优点：自动故障转移、动态添加节点、自动分布不同的节点、主从节点。缺点：节点间通信延迟、资源消耗 至少需要6个节点 （3主3从）

  ```go
  func New(ctx context.Context, cfg *config.Config) (*Service, error) {
  	var kvConf kv.KvConf // go-zero框架的kv配置 创建一个空切片
  	// 循环redis配置，将配置信息添加到kvConf切片中，用于后续创建kvStore对象。
  	for _, con := range cfg.Kv.Redis {
  		// 循环redis配置，将配置信息添加到kvConf切片中，用于后续创建kvStore对象。
  		kvConf = append(kvConf, cache.NodeConf{
  			RedisConf: redis.RedisConf{
  				Host: con.Host,
  				Type: con.Type,
  				Pass: con.Pass,
  			},
  			Weight: 2,
  		})
  	}
  	//1. 调用ckv NewStore函数创建rdis连接，返回一个 Store 对象，包含连接信息
  	kvStore := xkv.NewStore(kvConf)
  }
  ```

- **初始化mysql服务**
- **创建NFT项目白名单管理器(collectionFilter): **只处理白名单的合约的地址（NFT集合），从数据库中读取NFT白名单写入到内存中。collectionFilter可调用
- **创建NFT买卖订单管理器（orderManager）:**处理用户的买卖订单匹配和状态更新
  1. 
- **创建NFT订单簿同步器（orderbookIndexer）:**同步NFT项目的订单簿数据
  1. 































## 2.项目功能点

1. **cobra**： 

   1.1.  用于根据用户输入的命令，**动态判断**不同环境的配置文件。适用于开发环境、测试环境、生产环境。用户执行不同的命令。切换不同环境的配置参数。

   1.2.  分为**主命令和子命令**。判断是否有**run函数**来判断是否为子命令。子命令**触发时机**就是Cobra 解析命令行时候，发现有子命令。果主命令中有**cobra.OnInitialize(xxxx),**表示所有命令之前 调用的函数。会在这个函数**之后**执行子命令，确保主命令**初始化OnInitialize完成**，才会执行子命令。就像路由匹配一样，先匹配主路由再匹配子路由

   ```golang
   go get -u github.com/spf13/cobra@latest
   ```

2. **viper**： 基本上跟cobra**搭配**使用，用于保存cobra**解析**出的配置文件路径，以及**读取**配置文件参数。注意viper**全局单例**，全局可用。

   ```golang
   github.com/spf13/viper
   ```

   

3. **kv.store**,是一个**通用**的键值存储接口，是go-zero框架**提供**的存储抽象层。包含 **redis(单节点/集群/哨兵模式)**、**内存缓存(本地内存)**、**多级缓存**、**自定义存储后端**

​       3.1. **redis集群**， 操作**相同的**数据库以及缓存，所以可以考虑使用kv.Store，redis集群； 6381节点缓存的

​       可能因为**负载均衡**，跑到了6371那，但是取是正常取到。

​       使用的是go-zero的kv-store和go-zero的redis, 使用kv.Store将节点集合，创建成为一个分布式键值存储器。

​		将所有节点信息返回，一个

```golang
/*kv-store 和 redis 实现的应用层切片，伪集群  */
// service.go 业务层 传入redis配置信息
"github.com/zeromicro/go-zero/core/stores/cache"
"github.com/zeromicro/go-zero/core/stores/kv"
"github.com/zeromicro/go-zero/core/stores/redis"
"github.com/ProjectsTask/EasySwapBase/stores/xkv"


func New(ctx context.Context, cfg *config.Config) (*Service, error) {
    // go-zero框架的kv配置 创建一个空切片
    // 必须得使用kv.KvConf类型 不然创建时候解析不了
	var kvConf kv.KvConf 
	for _, con := range cfg.Kv.Redis {
		// 循环redis配置，将配置信息添加到kvConf切片中，用于后续创建kvStore对象。
		kvConf = append(kvConf, cache.NodeConf{
			RedisConf: redis.RedisConf{
				Host: con.Host,
				Type: con.Type,
				Pass: con.Pass,
			},
			Weight: 2,
		})
	}
    // 调用ckv NewStore函数
	kvStore := xkv.NewStore(kvConf)
    
}




// xkv.go 创建redis集群
"github.com/zeromicro/go-zero/core/stores/cache"
"github.com/zeromicro/go-zero/core/stores/kv"
"github.com/zeromicro/go-zero/core/stores/redis"


// NewStore 新建键值存取器
/*
  其实就是根据redes配置信息,链接redis。然后使用go-zero的kv.Store创建一个分布式键值存储器
  然后返回一个 Store 对象，包含连接信息
  --为什么不直接连接redis呢？
  因为go-zero的kv.Store支持多节点，可以实现分布式键值存储
  而直接连接redis只能连接一个节点，无法实现分布式键值存储


  这个项目其实属于单项目 多节点。EasySwapSync、EasySwapBase、EasySwapBackend
  虽然是独立项目，到那时操作相同的数据库以及缓存，所以可以考虑使用kv.Store，redis集群
  6381节点缓存的可能因为负载均衡，跑到了6371那，但是取是正常取到
*/
func NewStore(c kv.KvConf) *Store {
	// 证配置：确保有可用的缓存节点且权重合理
	if len(c) == 0 || cache.TotalWeights(c) <= 0 {
		log.Fatal("no cache nodes")
	}
	
    // 重点就是这块代码，新建redis客户端节点
    // kv.NewStore(c)创建一个分布式键值对存储器，将多个节点存储
    // 简单的就使用kv.Store调用，get、set
    // 复杂的就使用kv.Store.Redis.Lpush等
	cn := redis.MustNewRedis(c[0].RedisConf) // 新建redis客户端 选择第一个节点
	// 返回一个 Store 对象，包含连接信息
	return &Store{
		Store: kv.NewStore(c), // 创建分布式键值存储器（支持多节点）
		Redis: cn,             // 第一个节点的客户端
	}
}


//redis集群 抽象概念

config := kv.KvConf{
    {
        RedisConf: redis.RedisConf{Host: "localhost:6379"}, 
        Weight: 40,
    },
    {
        RedisConf: redis.RedisConf{Host: "localhost:6380"}, 
        Weight: 30,
    },
    {
        RedisConf: redis.RedisConf{Host: "localhost:6381"}, 
        Weight: 30,
    },
}

```

4. **读写锁(sync.RWMutex)**，项目**本地内存**中缓存的**共享数据**需要使用读写锁，**数据库查询**不需要读写锁。

   4.1 **读锁**: 支持并发，需要取消读锁，才可以写

   4.2 **读写锁**: 读不能写，需要取消读，才能接下来写

   4.3 **写锁**: 会阻塞，需要取消写锁

   ```golang
   /* 这里查询的是 f.set中是否存在这个NFT集合合约地址*/
   
   // Filter 是一个线程安全的结构体，用于存储字符串集合
   type Filter struct {
   	ctx     context.Context
   	db      *gorm.DB
   	chain   string
   	set     map[string]bool // 字符串集合
   	lock    *sync.RWMutex   // 读写互斥锁，保证线程安全
   	project string
   }
   
   
   // Contains 检查 Filter 是否包含指定元素
   // 元素在检查前会被转换为小写
   func (f *Filter) Contains(element string) bool {
   	f.lock.RLock()         // 获取读锁 (并发)
   	defer f.lock.RUnlock() // 函数返回时释放锁
   	_, exists := f.set[strings.ToLower(element)]
   	return exists
   }
   
   ```






### **tips:**

1. **设计表结构思考**

   1.1 考虑**高频和低频**，假设某个NFT信息，那可以将图片等大字段**分表**出来，因为更多的是查询NFT信息，而不是全部的元数据

   67

   

2. **redis缓存思考**

   2.1 **哪些需要缓存，哪些不需要缓存 缓存时间怎么考虑 ？？？**

   ​     2.1.1 考虑**高频和低频**，**假设1：**查询我所拥有的全部NFT信息，这个整个NFT交易时长快则几十秒、慢则几分钟。不是高频查询，所以可以将查询接口**缓存**30s,下次查询此接口，可直接返回结果。**假设2:** 我要查询1-2ETH价格的NFT数据，那其实链上的NFT都在**更新**的，防止用户看到的数据**不一致**，这个时候需要缓存时间低一点5~·10s,甚至**不缓存**，给pirce加上**索引**，提高**查询速度**。

   

   2.2 **那如果多个条件缓存，命中率低，参数顺序不一致，key也不一样怎么办？？？**

   ​     2.2.1  考虑**参数排序（sort）**,将参数按照首字母ASCLL排序，生成key命中率会提高，再加上合理的缓存过期清理。

   

   

3. **分布式锁跟读写锁 互斥锁（单机锁）的区别？？？？？**

​		3.1 **分布式锁**不是redis/mysql自带的功能，而是我们**利用redis/mysql的原子性操作特性来实现的一种设计模式**。当多个应用服务器连接同一个redis服务器/mysql数据库时，我们需要通过分布式锁来协调这些服务器对共享资源的访问，确保在分布式环境下数据操作的一致性。

​        **实现原理：**

- **Redis分布式锁**：利用Redis的`SetNX`命令的原子性，只有一个服务器能成功设置锁，针对**多应用服务器**，与redis和mysql是否的单体还是集群没有关系。

- **MySQL分布式锁**：利用`UPDATE（隐式锁）、SELECT FOR UPDATE（显式锁）`或`GET_LOCK()`函数实现跨服务器的互斥访问（建一张锁表包含锁的名称类型，然后可以通过数据库的特性**事务**等待操作完成，才结束**释放锁**，或者通过**sql语句**中的GET_LOCK() 实现**跨服务**的分布式锁）注意需要**手动释放**

  

​		3.2 **单机锁（互斥锁/读写锁）** ，单机锁主要用于**单个进程内的并发控制**，保护进程内存中的共享资源（如全局变量、缓存对象、连接池等），而不是直接用于数据库读写操作。数据库本身有自己的事务和锁机制来处理并发。

​        **使用场景：**

- 保护内存中的**共享变量**
- 控制对**本地资源**的访问
- 单机部署的应用程序













1. 区块链事件同步：

- 监听并解析链上的订单相关事件（创建、取消、撮合）

- 将这些事件数据同步到数据库中

2. 订单状态管理：

- 跟踪所有NFT订单的状态变化

- 更新订单状态（创建、取消、完成）

- 记录交易历史

3. 地板价维护：

- 定期（每10秒）查询和更新所有NFT集合的地板价

- 定期（每天）清理过期的地板价数据

- 为前端提供实时的地板价数据

4. NFT所有权跟踪：

- 在交易完成后更新NFT的所有者信息

- 维护用户的NFT资产记录

5. 交易活动记录：

- 记录所有交易活动（创建、取消、撮合）

- 提供交易历史查询功能

6. 数据提供：

- 为前端提供API接口，展示NFT集合的地板价

- 为用户提供其订单状态和交易历史

- 支持前端展示市场活动和趋势













**项目采用了分层架构设计**：

1. API 层：处理 HTTP 请求和响应

- 使用 Gin 框架构建 RESTful API

- 实现了中间件（认证、缓存、日志、错误恢复）

- 路由定义在 /api/router 目录下

1. 服务层：实现业务逻辑

- 位于 /service 目录

- 包含各种业务服务实现（用户、收藏品、订单等）

1. 数据访问层：

- 位于 /dao 目录

- 处理数据库操作和数据模型

1. 公共组件：

- 位于 /common 目录

- 包含工具函数和通用组件

### 主要功能

从路由定义可以看出，该项目提供以下主要功能：

1. 用户管理：

- 登录消息生成

- 用户登录

- 签名状态查询

1. NFT 收藏品管理：

- 收藏品详情查询

- 收藏品物品列表

- NFT 物品详情、特性、图片等信息查询

- 收藏品排名

1. 交易功能：

- 出价（Bid）管理

- 挂单（Listing）管理

- 历史销售记录查询

1. 活动记录：

- 多链活动信息查询

1. 用户投资组合：

- 用户拥有的收藏品

- 用户拥有的 NFT 物品

- 用户的挂单和出价信息

### 技术栈

1. Web 框架：Gin

1. 数据库：MySQL (通过 GORM)

1. 缓存：Redis

1. 区块链交互：go-ethereum

1. 配置管理：Viper

1. 日志：Zap

1. API 文档：Swagger