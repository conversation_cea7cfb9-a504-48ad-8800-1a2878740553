package service

import (
	"context"
	"fmt"
	"sync"

	"github.com/ProjectsTask/EasySwapBase/chain"
	"github.com/ProjectsTask/EasySwapBase/chain/chainclient"
	"github.com/ProjectsTask/EasySwapBase/ordermanager"
	"github.com/ProjectsTask/EasySwapBase/stores/xkv"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/kv"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"gorm.io/gorm"

	"github.com/ProjectsTask/EasySwapSync/service/orderbookindexer"

	"github.com/ProjectsTask/EasySwapSync/model"
	"github.com/ProjectsTask/EasySwapSync/service/collectionfilter"
	"github.com/ProjectsTask/EasySwapSync/service/config"
)

type Service struct {
	ctx              context.Context
	config           *config.Config
	kvStore          *xkv.Store
	db               *gorm.DB
	wg               *sync.WaitGroup
	collectionFilter *collectionfilter.Filter
	orderbookIndexer *orderbookindexer.Service
	orderManager     *ordermanager.OrderManager
}

/*
初始化服务 主集合
*/
func New(ctx context.Context, cfg *config.Config) (*Service, error) {
	var kvConf kv.KvConf // go-zero框架的kv配置 创建一个空切片
	// 循环redis配置，将配置信息添加到kvConf切片中，用于后续创建kvStore对象。
	for _, con := range cfg.Kv.Redis {
		// 循环redis配置，将配置信息添加到kvConf切片中，用于后续创建kvStore对象。
		kvConf = append(kvConf, cache.NodeConf{
			RedisConf: redis.RedisConf{
				Host: con.Host,
				Type: con.Type,
				Pass: con.Pass,
			},
			Weight: 2,
		})
	}
	//1. 调用ckv NewStore函数创建rdis连接，返回一个 Store 对象，包含连接信息
	kvStore := xkv.NewStore(kvConf)

	var err error

	//2. 创建数据库连接
	db := model.NewDB(cfg.DB)

	//3. 创建NFT项目白名单管理器，只处理平台支持的NFT项目
	collectionFilter := collectionfilter.New(ctx, db, cfg.ChainCfg.Name, cfg.ProjectCfg.Name)

	//4. 创建NFT买卖订单管理器，处理用户的买单卖单匹配和状态更新
	orderManager := ordermanager.New(ctx, db, kvStore, cfg.ChainCfg.Name, cfg.ProjectCfg.Name)

	// 创建NFT订单簿同步器，同步NFT项目订单簿数据
	var orderbookSyncer *orderbookindexer.Service
	// 创建链客户端`chainclient`
	var chainClient chainclient.ChainClient
	// 打印链客户端的URL
	fmt.Println("chainClient url:" + cfg.AnkrCfg.HttpsUrl + cfg.AnkrCfg.ApiKey)

	//5. 创建链客户端 链接太坊网络
	chainClient, err = chainclient.New(int(cfg.ChainCfg.ID), cfg.AnkrCfg.HttpsUrl+cfg.AnkrCfg.ApiKey)
	if err != nil {
		return nil, errors.Wrap(err, "failed on create evm client")
	}

	// 6. 创建订单簿同步器 处理区块链获取数据并存储数据
	switch cfg.ChainCfg.ID {
	case chain.EthChainID, chain.OptimismChainID, chain.SepoliaChainID: // 判断是否为指定链
		orderbookSyncer = orderbookindexer.New(ctx, cfg, db, kvStore, chainClient, cfg.ChainCfg.ID, cfg.ChainCfg.Name, orderManager)
	}
	if err != nil {
		return nil, errors.Wrap(err, "failed on create trade info server")
	}

	manager := Service{
		ctx:              ctx,               // 上下文，用于控制整个服务的生命周期
		config:           cfg,               // 全局配置信息（数据库、Redis、链配置等）
		db:               db,                // 全局配置信息（数据库、Redis、链配置等）
		kvStore:          kvStore,           // Redis缓存连接，用于高速数据存取
		collectionFilter: collectionFilter,  // NFT项目白名单过滤器(NFT集合，也就是合约地址白名单)
		orderbookIndexer: orderbookSyncer,   // 订单簿同步器（你子Service）
		orderManager:     orderManager,      // 订单管理器
		wg:               &sync.WaitGroup{}, //Redis缓存连接，用于高速数据存取
	}
	return &manager, nil
}

func (s *Service) Start() error {
	// 不要移动位置 将数据库中支持的NFT集合地址，加载到内存中
	if err := s.collectionFilter.PreloadCollections(); err != nil {
		return errors.Wrap(err, "failed on preload collection to filter")
	}
	// 启动订单簿同步器 
	s.orderbookIndexer.Start()
	//
	s.orderManager.Start()
	return nil
}
